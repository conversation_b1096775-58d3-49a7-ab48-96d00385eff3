from __future__ import annotations

from enum import Enum

from flask_appbuilder import Model
from sqlalchemy import Column, ForeignKey, Integer, String
from sqlalchemy.orm import relationship

from superset.models.helpers import AuditMixinNullable


class FolderType(Enum):
    GLOBAL = 0
    TEAM = 1
    PLUGIN = 2
    PERSONAL = 3


class FolderLevel(Enum):
    ROOT = 0
    SYSTEM = 1
    USER = 2


class FolderItemType(Enum):
    FOLDER = 0
    DASHBOARD = 1


FOLDER_ADMIN_ROLE = {
    FolderType.GLOBAL: "folder_admin_global",
    FolderType.TEAM: "folder_admin_team",
    FolderType.PLUGIN: "folder_admin_plugin",
}


class Folder(Model, AuditMixinNullable):
    __tablename__ = "folders"

    id = Column(Integer, primary_key=True)
    name_ru = Column(String, nullable=False)
    name_en = Column(String, nullable=False)
    description_ru = Column(String, nullable=False)
    description_en = Column(String, nullable=False)
    type = Column(Integer, nullable=False)
    level = Column(Integer, nullable=False)
    team_id = Column(Integer, ForeignKey("teams.id"), nullable=True)
    parent_id = Column(Integer, nullable=True)
    slug = Column(String, nullable=True)
    order = Column(Integer, nullable=False, default=0)

    team = relationship("Team", foreign_keys=[team_id], backref="folders")

    def __repr__(self) -> str:
        return self.name_en


class FolderItem(Model, AuditMixinNullable):
    __tablename__ = "folder_items"

    id = Column(Integer, primary_key=True)
    folder_id = Column(Integer, ForeignKey("folders.id"), nullable=False)
    item_id = Column(Integer, nullable=False)
    item_type = Column(Integer, nullable=False)
    order = Column(Integer, nullable=False, default=0)

    folder = relationship("Folder", foreign_keys=[folder_id], backref="items")
